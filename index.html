<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>CC Sanction Letter - APCOB</title>
    <style>
        body {
            font-family: 'Times New Roman', serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            font-size: 12px;
            line-height: 1.3;
        }

        .document {
            width: 210mm;
            height: 297mm;
            margin: 10mm auto;
            background: white;
            padding: 15mm;
            box-sizing: border-box;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            position: relative;
        }

        /* Mobile Devices (320px - 480px) */
        @media screen and (max-width: 480px) {
            body {
                font-size: 10px;
                background-color: white;
            }

            .document {
                width: 100%;
                height: auto;
                margin: 5px;
                padding: 10px;
                box-shadow: none;
                min-height: auto;
            }

            .header {
                flex-direction: column;
                text-align: center;
                padding: 5px;
                margin-bottom: 10px;
            }

            .header-left img,
            .anniversary-logo img {
                width: 50px;
                height: 50px;
            }

            .bank-name {
                font-size: 14px;
                margin: 5px 0;
            }

            .bank-subtitle {
                font-size: 10px;
            }

            .ref-line {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }

            .address-lines input {
                width: 95%;
            }

            .details-table {
                font-size: 9px;
            }

            .details-table th,
            .details-table td {
                padding: 2px;
            }

            input {
                font-size: 9px;
                min-width: 30px;
            }

            .wide-input {
                min-width: 80px;
            }

            .medium-input {
                min-width: 50px;
            }

            .small-input {
                min-width: 25px;
            }
        }

        /* Tablet Portrait (481px - 768px) */
        @media screen and (min-width: 481px) and (max-width: 768px) {
            .document {
                width: 95%;
                height: auto;
                margin: 10px auto;
                padding: 12px;
                min-height: auto;
            }

            .header {
                padding: 8px;
            }

            .header-left img,
            .anniversary-logo img {
                width: 60px;
                height: 60px;
            }

            .bank-name {
                font-size: 15px;
            }

            .details-table {
                font-size: 10px;
            }

            input {
                font-size: 10px;
            }
        }

        /* Tablet Landscape (769px - 1024px) */
        @media screen and (min-width: 769px) and (max-width: 1024px) {
            .document {
                width: 90%;
                max-width: 750px;
                height: auto;
                margin: 15px auto;
                padding: 14px;
                min-height: auto;
            }

            .header-left img,
            .anniversary-logo img {
                width: 70px;
                height: 70px;
            }
        }

        /* Laptop (1025px - 1366px) */
        @media screen and (min-width: 1025px) and (max-width: 1366px) {
            .document {
                width: 210mm;
                height: 297mm;
                margin: 15mm auto;
                padding: 15mm;
            }
        }

        /* Desktop (1367px - 1920px) */
        @media screen and (min-width: 1367px) and (max-width: 1920px) {
            .document {
                width: 210mm;
                height: 297mm;
                margin: 20mm auto;
                padding: 15mm;
            }
        }

        /* Large Desktop (1921px and above) */
        @media screen and (min-width: 1921px) {
            .document {
                width: 210mm;
                height: 297mm;
                margin: 25mm auto;
                padding: 15mm;
                transform: scale(1.1);
            }
        }

        @media print {
            body {
                background-color: white;
                margin: 0;
                padding: 0;
            }

            .document {
                width: 210mm;
                height: 297mm;
                margin: 0;
                padding: 15mm;
                box-shadow: none;
                page-break-after: avoid;
                transform: none;
            }

            .header {
                flex-direction: row;
            }

            .ref-line {
                flex-direction: row;
            }
        }

        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px;
            margin-bottom: 15px;
        }

        .header-left img {
            width: 80px;
            height: 80px;
        }

        .header-center {
            flex: 2;
            text-align: center;
        }

        .bank-name {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 3px;
        }

        .bank-subtitle {
            font-size: 11px;
            font-style: italic;
        }

        .header-right {
            text-align: right;
        }

        .ldn {
            font-weight: bold;
            margin-bottom: 8px;
        }

        .anniversary-logo img {
            width: 60px;
            height: 60px;
        }

        input {
            border: none;
            border-bottom: 1px solid #000;
            background: transparent;
            font-size: 11px;
            font-family: 'Times New Roman', serif;
            width: auto;
            min-width: 40px;
            padding: 1px 2px;
            outline: none;
            margin: 0 1px;
        }

        input:focus {
            border-bottom: 1px solid #000;
            background-color: transparent;
        }

        input[type="date"] {
            min-width: 80px;
            font-size: 11px;
        }

        input[type="text"] {
            min-width: 50px;
        }

        .wide-input {
            min-width: 120px;
        }

        .medium-input {
            min-width: 70px;
        }

        .small-input {
            min-width: 35px;
        }

        .title {
            text-align: center;
            font-weight: bold;
            text-decoration: underline;
            margin: 12px 0;
            font-size: 13px;
        }

        .address-section {
            margin: 12px 0;
        }

        .address-lines input {
            display: block;
            width: 80%;
            border: none;
            border-bottom: 1px solid #000;
            margin-bottom: 3px;
            font-size: 11px;
            padding: 2px;
        }

        .ref-line {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            font-size: 11px;
        }

        .subject-line,
        .reference-line {
            margin: 10px 0;
            font-size: 11px;
            line-height: 1.4;
            text-align: justify;
        }

        .salutation {
            margin: 15px 0 10px 0;
        }

        .content-paragraph {
            margin: 12px 0;
            text-align: justify;
            font-size: 11px;
            line-height: 1.4;
        }

        .details-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 11px;
        }

        .details-table th,
        .details-table td {
            border: 1px solid #000;
            padding: 4px;
            vertical-align: top;
        }

        .details-table th {
            background-color: #f5f5f5;
            text-align: center;
            font-weight: bold;
        }

        .sno-col {
            width: 8%;
            text-align: center;
        }

        .particulars-col {
            width: 45%;
        }

        .details-col {
            width: 47%;
        }
    </style>
</head>

<body>
    <div class="document">
        <div class="header">
            <div class="header-left">
                <img src="i1.png" alt="APCOB Logo">
            </div>
            <div class="header-center">
                <div class="bank-name">The Andhra Pradesh<br>State Cooperative Bank Ltd.</div>
                <div class="bank-subtitle">(A State Partnered Scheduled Bank)</div>
            </div>
            <div class="header-right">
                <div class="ldn">LDN</div>
                <div class="anniversary-logo">
                    <img src="i2.png" alt="Anniversary Logo">
                </div>
            </div>
        </div>

        <div class="ref-line">
            <span><input type="text" class="medium-input"> Br/ <input type="text" class="medium-input"> loan /
                20_-20</span>
            <span>Date: <input type="text" class="medium-input"></span>
        </div>

        <div class="title">CC SANCTION LETTER</div>

        <div class="address-section">
            <div>To,</div>
            <div class="address-lines"><input type="text"></div>
            <div class="address-lines"><input type="text"></div>
            <div class="address-lines"><input type="text"></div>
        </div>

        <div class="salutation">Sir/Madam,</div>

        <div class="subject-line">
            <strong>Sub:</strong> APCOB-<input type="text" class="medium-input"> br <input type="text"
                class="wide-input"> (name of borrower) -
            Sanction of Cash Credit/Overdraft limit of Rs. <input type="text" class="medium-input"> @
            <input type="text" class="small-input"> % p.a under <input type="text" class="medium-input"> loan -
            Communication of.
        </div>

        <div class="reference-line">
            <strong>Ref:</strong> Your <input type="text" class="medium-input"> loan application Dt: <input type="text"
                class="medium-input">
        </div>

        <div style="text-align: center; margin: 15px 0;">***</div>

        <div class="content-paragraph">
            With reference to your application under reference, we are pleased to inform you that, the competent
            Authority has
            sanctioned your loan facility with terms & conditions as detailed below:
        </div>

        <table class="details-table">
            <thead>
                <tr>
                    <th class="sno-col">S No.</th>
                    <th class="particulars-col">Particulars</th>
                    <th class="details-col">Details</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="sno-col">1.</td>
                    <td>Name of the Applicant(s)</td>
                    <td>
                        1. <input type="text" class="wide-input"> (Applicant)<br>
                        2. <input type="text" class="wide-input"> (Co-Applicant/Guarantor)
                    </td>
                </tr>
                <tr>
                    <td class="sno-col">2.</td>
                    <td>Name of the Loan</td>
                    <td><input type="text" class="wide-input"></td>
                </tr>
                <tr>
                    <td class="sno-col">3.</td>
                    <td>Sanctioned Limit</td>
                    <td>CC/OD Rs. <input type="text" class="medium-input"> (Rupees <input type="text"
                            class="wide-input"> only)</td>
                </tr>
                <tr>
                    <td class="sno-col">4.</td>
                    <td>Risk category of the Borrower</td>
                    <td><input type="text" class="medium-input"></td>
                </tr>
                <tr>
                    <td class="sno-col">5.</td>
                    <td>Rate of Interest</td>
                    <td><input type="text" class="small-input">% p.a (on monthly rests)</td>
                </tr>
                <tr>
                    <td class="sno-col">6.</td>
                    <td>Monthly Interest (Amt in Rs.)</td>
                    <td><input type="text" class="medium-input"></td>
                </tr>
            </tbody>
        </table>
    </div>
</body>

</html>